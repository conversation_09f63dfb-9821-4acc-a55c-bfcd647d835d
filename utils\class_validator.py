"""
نظام فحص وتحقق من سلامة الكلاسات لمنع مشاكل التهيئة
"""

import inspect
import importlib
import sys
from PyQt5.QtWidgets import QWidget, QDialog
from typing import Dict, List, Any, Optional

class ClassValidator:
    """فاحص سلامة الكلاسات"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.validated_classes = {}
    
    def validate_widget_class(self, class_obj, module_name: str) -> bool:
        """فحص كلاس ويدجت"""
        try:
            class_name = class_obj.__name__
            
            # فحص الوراثة
            if not (issubclass(class_obj, QWidget) or issubclass(class_obj, QDialog)):
                self.errors.append(f"{module_name}.{class_name}: يجب أن يرث من QWidget أو QDialog")
                return False
            
            # فحص دالة __init__
            init_method = getattr(class_obj, '__init__', None)
            if not init_method:
                self.errors.append(f"{module_name}.{class_name}: لا توجد دالة __init__")
                return False
            
            # فحص معاملات __init__
            sig = inspect.signature(init_method)
            params = list(sig.parameters.keys())
            
            if 'self' not in params:
                self.errors.append(f"{module_name}.{class_name}: معامل self مفقود في __init__")
                return False
            
            # فحص معاملات شائعة
            expected_params = ['parent', 'session']
            for param in expected_params:
                if param in params:
                    param_obj = sig.parameters[param]
                    if param_obj.default is inspect.Parameter.empty and param != 'session':
                        self.warnings.append(f"{module_name}.{class_name}: معامل {param} ليس له قيمة افتراضية")
            
            # فحص الدوال المطلوبة
            required_methods = []
            if issubclass(class_obj, QWidget):
                required_methods = ['init_ui', 'setup_ui']  # واحدة منهما على الأقل
            
            has_required_method = False
            for method_name in required_methods:
                if hasattr(class_obj, method_name):
                    has_required_method = True
                    break
            
            if required_methods and not has_required_method:
                self.warnings.append(f"{module_name}.{class_name}: لا توجد دالة تهيئة واجهة ({', '.join(required_methods)})")
            
            self.validated_classes[f"{module_name}.{class_name}"] = {
                'status': 'valid',
                'params': params,
                'methods': [name for name, _ in inspect.getmembers(class_obj, predicate=inspect.isfunction)]
            }
            
            return True
            
        except Exception as e:
            self.errors.append(f"خطأ في فحص {module_name}.{class_name}: {str(e)}")
            return False
    
    def validate_module(self, module_name: str) -> Dict[str, Any]:
        """فحص وحدة كاملة"""
        try:
            module = importlib.import_module(module_name)
            results = {
                'module': module_name,
                'classes': [],
                'errors': [],
                'warnings': []
            }
            
            # البحث عن جميع الكلاسات
            for name, obj in inspect.getmembers(module, predicate=inspect.isclass):
                # تجاهل الكلاسات المستوردة
                if obj.__module__ != module_name:
                    continue
                
                # فحص كلاسات الويدجت فقط
                if issubclass(obj, (QWidget, QDialog)):
                    is_valid = self.validate_widget_class(obj, module_name)
                    results['classes'].append({
                        'name': name,
                        'valid': is_valid,
                        'type': 'QWidget' if issubclass(obj, QWidget) else 'QDialog'
                    })
            
            results['errors'] = [e for e in self.errors if module_name in e]
            results['warnings'] = [w for w in self.warnings if module_name in w]
            
            return results
            
        except Exception as e:
            return {
                'module': module_name,
                'classes': [],
                'errors': [f"فشل في تحميل الوحدة: {str(e)}"],
                'warnings': []
            }
    
    def validate_all_ui_modules(self) -> Dict[str, Any]:
        """فحص جميع وحدات الواجهة"""
        ui_modules = [
            'ui.main_window',
            'ui.clients',
            'ui.suppliers', 
            'ui.employees',
            'ui.projects',
            'ui.inventory',
            'ui.purchases',
            'ui.sales',
            'ui.expenses',
            'ui.revenues',
            'ui.invoices',
            'ui.notifications',
            'ui.reports'
        ]
        
        results = {
            'total_modules': len(ui_modules),
            'validated_modules': 0,
            'total_errors': 0,
            'total_warnings': 0,
            'modules': {}
        }
        
        for module_name in ui_modules:
            try:
                module_result = self.validate_module(module_name)
                results['modules'][module_name] = module_result
                results['validated_modules'] += 1
                results['total_errors'] += len(module_result['errors'])
                results['total_warnings'] += len(module_result['warnings'])
            except Exception as e:
                results['modules'][module_name] = {
                    'module': module_name,
                    'classes': [],
                    'errors': [f"فشل في الفحص: {str(e)}"],
                    'warnings': []
                }
                results['total_errors'] += 1
        
        return results
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """إنشاء تقرير مفصل"""
        report = []
        report.append("=" * 60)
        report.append("تقرير فحص سلامة الكلاسات")
        report.append("=" * 60)
        report.append(f"إجمالي الوحدات: {results['total_modules']}")
        report.append(f"الوحدات المفحوصة: {results['validated_modules']}")
        report.append(f"إجمالي الأخطاء: {results['total_errors']}")
        report.append(f"إجمالي التحذيرات: {results['total_warnings']}")
        report.append("")
        
        for module_name, module_data in results['modules'].items():
            report.append(f"📁 {module_name}")
            report.append("-" * 40)
            
            if module_data['classes']:
                report.append("الكلاسات:")
                for cls in module_data['classes']:
                    status = "✅" if cls['valid'] else "❌"
                    report.append(f"  {status} {cls['name']} ({cls['type']})")
            else:
                report.append("  لا توجد كلاسات ويدجت")
            
            if module_data['errors']:
                report.append("الأخطاء:")
                for error in module_data['errors']:
                    report.append(f"  ❌ {error}")
            
            if module_data['warnings']:
                report.append("التحذيرات:")
                for warning in module_data['warnings']:
                    report.append(f"  ⚠️ {warning}")
            
            report.append("")
        
        return "\n".join(report)

def run_validation():
    """تشغيل فحص شامل"""
    print("🔍 بدء فحص سلامة الكلاسات...")
    
    validator = ClassValidator()
    results = validator.validate_all_ui_modules()
    
    # إنشاء التقرير
    report = validator.generate_report(results)
    
    # طباعة التقرير
    print(report)
    
    # حفظ التقرير في ملف
    try:
        with open('class_validation_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print("📄 تم حفظ التقرير في class_validation_report.txt")
    except Exception as e:
        print(f"❌ فشل في حفظ التقرير: {str(e)}")
    
    # إرجاع النتائج
    return results

if __name__ == "__main__":
    run_validation()
