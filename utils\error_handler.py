"""
نظام معالجة الأخطاء الشامل لمنع تعليق البرنامج
"""

import sys
import traceback
import logging
from PyQt5.QtWidgets import QMessageBox, QApplication
from PyQt5.QtCore import QObject, pyqtSignal
from functools import wraps
import inspect

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app_errors.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ErrorHandler(QObject):
    """معالج الأخطاء الشامل"""
    
    error_occurred = pyqtSignal(str, str)  # نوع الخطأ، رسالة الخطأ
    
    def __init__(self):
        super().__init__()
        self.setup_exception_handler()
    
    def setup_exception_handler(self):
        """إعداد معالج الاستثناءات العام"""
        sys.excepthook = self.handle_exception
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالجة الاستثناءات غير المتوقعة"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logging.error(f"خطأ غير متوقع: {error_msg}")
        
        # إظهار رسالة خطأ للمستخدم
        try:
            if QApplication.instance():
                QMessageBox.critical(
                    None, 
                    "خطأ في البرنامج", 
                    f"حدث خطأ غير متوقع:\n{str(exc_value)}\n\nسيتم حفظ تفاصيل الخطأ في ملف السجل."
                )
        except:
            pass
        
        self.error_occurred.emit(str(exc_type.__name__), str(exc_value))

def safe_execute(func):
    """ديكوريتر لتنفيذ آمن للدوال"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = f"خطأ في {func.__name__}: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            
            # إرجاع قيمة افتراضية حسب نوع الدالة
            if 'init' in func.__name__:
                return None
            elif func.__name__.startswith('get_') or func.__name__.startswith('load_'):
                return []
            else:
                return False
    return wrapper

def validate_widget_params(parent=None, session=None, **kwargs):
    """التحقق من صحة معاملات الويدجت"""
    errors = []
    
    # التحقق من parent
    if parent is not None:
        from PyQt5.QtWidgets import QWidget
        if not isinstance(parent, QWidget):
            errors.append(f"parent يجب أن يكون QWidget، تم تمرير: {type(parent)}")
    
    # التحقق من session
    if session is not None:
        if not hasattr(session, 'query'):
            errors.append(f"session يجب أن يحتوي على query method، تم تمرير: {type(session)}")
    
    if errors:
        raise ValueError("معاملات خاطئة: " + "; ".join(errors))
    
    return True

def safe_widget_init(widget_class):
    """ديكوريتر لتهيئة آمنة للويدجت"""
    original_init = widget_class.__init__
    
    @wraps(original_init)
    def safe_init(self, *args, **kwargs):
        try:
            # استخراج المعاملات المتوقعة
            sig = inspect.signature(original_init)
            bound_args = sig.bind(self, *args, **kwargs)
            bound_args.apply_defaults()
            
            # التحقق من المعاملات
            params = dict(bound_args.arguments)
            params.pop('self', None)
            validate_widget_params(**params)
            
            # تنفيذ التهيئة الأصلية
            return original_init(self, *args, **kwargs)
            
        except Exception as e:
            error_msg = f"خطأ في تهيئة {widget_class.__name__}: {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            
            # تهيئة أساسية آمنة
            try:
                super(widget_class, self).__init__()
                if hasattr(self, 'session') and len(args) > 0:
                    self.session = args[0] if hasattr(args[0], 'query') else None
                if hasattr(self, 'parent_widget'):
                    self.parent_widget = kwargs.get('parent') or (args[1] if len(args) > 1 else None)
            except:
                pass
    
    widget_class.__init__ = safe_init
    return widget_class

def check_imports():
    """فحص الاستيرادات المطلوبة"""
    required_modules = [
        'PyQt5.QtWidgets',
        'PyQt5.QtCore', 
        'PyQt5.QtGui',
        'sqlalchemy'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        error_msg = f"مكتبات مفقودة: {', '.join(missing_modules)}"
        logging.error(error_msg)
        raise ImportError(error_msg)
    
    return True

def safe_database_operation(func):
    """ديكوريتر للعمليات الآمنة مع قاعدة البيانات"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = f"خطأ في قاعدة البيانات ({func.__name__}): {str(e)}"
            logging.error(error_msg)
            print(f"❌ {error_msg}")
            
            # محاولة استرداد الجلسة
            if len(args) > 0 and hasattr(args[0], 'session'):
                try:
                    args[0].session.rollback()
                except:
                    pass
            
            return None
    return wrapper

# إنشاء معالج الأخطاء العام
error_handler = ErrorHandler()

def initialize_error_handling():
    """تهيئة نظام معالجة الأخطاء"""
    try:
        check_imports()
        print("✅ تم تهيئة نظام معالجة الأخطاء بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في تهيئة نظام معالجة الأخطاء: {str(e)}")
        return False
