#!/usr/bin/env python3
"""
اختبار شامل لوظائف قاعدة البيانات والتحقق من حفظ البيانات الفعلية
"""

import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الحالي لـ Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import (
    get_session, init_db, Client, Supplier, Employee, Project, 
    Sale, Purchase, Inventory, Expense, Revenue, Property, 
    Reminder, Invoice, DailyWage
)

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔗 اختبار الاتصال بقاعدة البيانات...")
    try:
        session = get_session()
        # اختبار بسيط للاتصال
        from sqlalchemy import text
        session.execute(text("SELECT 1"))
        session.close()
        print("✅ الاتصال بقاعدة البيانات يعمل بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
        return False

def test_clients_crud():
    """اختبار عمليات العملاء (إنشاء، قراءة، تحديث، حذف)"""
    print("\n👥 اختبار عمليات العملاء...")
    session = get_session()
    
    try:
        # إنشاء عميل جديد
        test_client = Client(
            name="عميل اختبار",
            phone="01234567890",
            email="<EMAIL>",
            address="عنوان اختبار",
            balance=1000.0,
            notes="عميل للاختبار"
        )
        session.add(test_client)
        session.commit()
        
        # قراءة العميل
        saved_client = session.query(Client).filter_by(name="عميل اختبار").first()
        assert saved_client is not None, "فشل في حفظ العميل"
        assert saved_client.balance == 1000.0, "فشل في حفظ رصيد العميل"
        
        # تحديث العميل
        saved_client.balance = 1500.0
        session.commit()
        
        # التحقق من التحديث
        updated_client = session.query(Client).filter_by(name="عميل اختبار").first()
        assert updated_client.balance == 1500.0, "فشل في تحديث رصيد العميل"
        
        # حذف العميل
        session.delete(saved_client)
        session.commit()
        
        # التحقق من الحذف
        deleted_client = session.query(Client).filter_by(name="عميل اختبار").first()
        assert deleted_client is None, "فشل في حذف العميل"
        
        print("✅ عمليات العملاء تعمل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في عمليات العملاء: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def test_sales_crud():
    """اختبار عمليات المبيعات"""
    print("\n🛍️ اختبار عمليات المبيعات...")
    session = get_session()
    
    try:
        # إنشاء عميل للاختبار
        test_client = Client(
            name="عميل مبيعات",
            phone="01111111111",
            email="<EMAIL>",
            address="عنوان المبيعات"
        )
        session.add(test_client)
        session.commit()
        
        # إنشاء مبيعة جديدة
        test_sale = Sale(
            client_id=test_client.id,
            sale_number="TEST-001",
            total_amount=2500.0,
            paid_amount=2000.0,
            discount_amount=100.0,
            tax_amount=250.0,
            status="completed",
            payment_method="cash",
            notes="مبيعة اختبار",
            date=datetime.now()
        )
        session.add(test_sale)
        session.commit()
        
        # قراءة المبيعة
        saved_sale = session.query(Sale).filter_by(sale_number="TEST-001").first()
        assert saved_sale is not None, "فشل في حفظ المبيعة"
        assert saved_sale.total_amount == 2500.0, "فشل في حفظ مبلغ المبيعة"
        assert saved_sale.status == "completed", "فشل في حفظ حالة المبيعة"
        
        # تنظيف البيانات
        session.delete(saved_sale)
        session.delete(test_client)
        session.commit()
        
        print("✅ عمليات المبيعات تعمل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في عمليات المبيعات: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def test_inventory_crud():
    """اختبار عمليات المخزون"""
    print("\n📦 اختبار عمليات المخزون...")
    session = get_session()
    
    try:
        # إنشاء عنصر مخزون جديد
        test_item = Inventory(
            name="منتج اختبار",
            category="فئة اختبار",
            unit="قطعة",
            quantity=100,
            min_quantity=10,
            cost_price=50.0,
            selling_price=75.0,
            location="مخزن اختبار",
            notes="عنصر للاختبار"
        )
        session.add(test_item)
        session.commit()
        
        # قراءة العنصر
        saved_item = session.query(Inventory).filter_by(name="منتج اختبار").first()
        assert saved_item is not None, "فشل في حفظ عنصر المخزون"
        assert saved_item.quantity == 100, "فشل في حفظ كمية المخزون"
        assert saved_item.cost_price == 50.0, "فشل في حفظ سعر التكلفة"
        
        # تحديث الكمية
        saved_item.quantity = 80
        session.commit()
        
        # التحقق من التحديث
        updated_item = session.query(Inventory).filter_by(name="منتج اختبار").first()
        assert updated_item.quantity == 80, "فشل في تحديث كمية المخزون"
        
        # تنظيف البيانات
        session.delete(saved_item)
        session.commit()
        
        print("✅ عمليات المخزون تعمل بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في عمليات المخزون: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def test_suppliers_crud():
    """اختبار عمليات الموردين"""
    print("\n🚛 اختبار عمليات الموردين...")
    session = get_session()

    try:
        # إنشاء مورد جديد
        test_supplier = Supplier(
            name="مورد اختبار",
            phone="01555555555",
            email="<EMAIL>",
            address="عنوان المورد",
            balance=-500.0,
            notes="مورد للاختبار"
        )
        session.add(test_supplier)
        session.commit()

        # قراءة المورد
        saved_supplier = session.query(Supplier).filter_by(name="مورد اختبار").first()
        assert saved_supplier is not None, "فشل في حفظ المورد"
        assert saved_supplier.balance == -500.0, "فشل في حفظ رصيد المورد"

        # تنظيف البيانات
        session.delete(saved_supplier)
        session.commit()

        print("✅ عمليات الموردين تعمل بنجاح")
        return True

    except Exception as e:
        print(f"❌ فشل في عمليات الموردين: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def test_employees_crud():
    """اختبار عمليات العمال"""
    print("\n👷‍♂️ اختبار عمليات العمال...")
    session = get_session()

    try:
        # إنشاء عامل جديد
        test_employee = Employee(
            name="عامل اختبار",
            phone="01666666666",
            email="<EMAIL>",
            address="عنوان العامل",
            position="مهندس",
            salary=5000.0,
            balance=200.0,
            notes="عامل للاختبار"
        )
        session.add(test_employee)
        session.commit()

        # قراءة العامل
        saved_employee = session.query(Employee).filter_by(name="عامل اختبار").first()
        assert saved_employee is not None, "فشل في حفظ العامل"
        assert saved_employee.salary == 5000.0, "فشل في حفظ راتب العامل"

        # تنظيف البيانات
        session.delete(saved_employee)
        session.commit()

        print("✅ عمليات العمال تعمل بنجاح")
        return True

    except Exception as e:
        print(f"❌ فشل في عمليات العمال: {e}")
        session.rollback()
        return False
    finally:
        session.close()

def test_all_sections():
    """اختبار جميع الأقسام"""
    print("\n🔍 اختبار جميع الأقسام...")

    sections_tests = [
        ("قاعدة البيانات", test_database_connection),
        ("العملاء", test_clients_crud),
        ("الموردين", test_suppliers_crud),
        ("العمال", test_employees_crud),
        ("المبيعات", test_sales_crud),
        ("المخزون", test_inventory_crud),
    ]
    
    passed = 0
    total = len(sections_tests)
    
    for section_name, test_func in sections_tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار {section_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {section_name}: {e}")
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"✅ نجح: {passed}/{total}")
    print(f"❌ فشل: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج مراجعة")
        return False

def check_database_tables():
    """التحقق من وجود جميع الجداول"""
    print("\n📋 التحقق من جداول قاعدة البيانات...")
    session = get_session()
    
    try:
        tables_to_check = [
            ("العملاء", Client),
            ("الموردين", Supplier),
            ("العمال", Employee),
            ("المشاريع", Project),
            ("المبيعات", Sale),
            ("المشتريات", Purchase),
            ("المخزون", Inventory),
            ("المصروفات", Expense),
            ("الإيرادات", Revenue),
            ("العقارات", Property),
            ("التنبيهات", Reminder),
            ("الفواتير", Invoice),
            ("الأجور اليومية", DailyWage),
        ]
        
        for table_name, model_class in tables_to_check:
            try:
                count = session.query(model_class).count()
                print(f"✅ جدول {table_name}: {count} سجل")
            except Exception as e:
                print(f"❌ مشكلة في جدول {table_name}: {e}")
        
        print("✅ تم التحقق من جميع الجداول")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من الجداول: {e}")
        return False
    finally:
        session.close()

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 بدء الاختبار الشامل لوظائف قاعدة البيانات")
    print("=" * 60)
    
    # تهيئة قاعدة البيانات
    try:
        init_db()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"❌ فشل في تهيئة قاعدة البيانات: {e}")
        return
    
    # التحقق من الجداول
    check_database_tables()
    
    # تشغيل الاختبارات
    success = test_all_sections()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 الاختبار مكتمل بنجاح! البرنامج جاهز للاستخدام الفعلي")
    else:
        print("⚠️ الاختبار كشف عن مشاكل تحتاج إصلاح")

if __name__ == "__main__":
    main()
