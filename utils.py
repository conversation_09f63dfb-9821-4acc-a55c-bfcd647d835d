import datetime
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QDate
import re

# دالة مساعدة للحصول على معلومات الشاشة والتكيف مع الحجم
def get_screen_adaptive_settings():
    """الحصول على إعدادات التكيف مع الشاشة مع حل مشكلة التداخلات"""
    try:
        from PyQt5.QtWidgets import QApplication

        # الحصول على أبعاد الشاشة المتاحة (بدون شريط المهام)
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        screen_width = screen_geometry.width()
        screen_height = screen_geometry.height()

        # حساب الحد الأدنى المطلوب للعرض والارتفاع
        min_safe_width = 300
        min_safe_height = 150

        # تحديد نوع الشاشة والإعدادات المناسبة مع هامش أمان
        if screen_width <= 800 or screen_height <= 600:
            # شاشة صغيرة جداً - تقليل كبير
            return {
                'type': 'tiny',
                'max_message_length': 50,  # نص قصير جداً
                'dialog_width': min(250, int(screen_width * 0.85)),
                'dialog_height': min(120, int(screen_height * 0.4)),
                'font_size': 8,
                'use_compact_mode': True
            }
        elif screen_width <= 1024 or screen_height <= 768:
            # شاشة صغيرة
            return {
                'type': 'small',
                'max_message_length': 80,
                'dialog_width': min(320, int(screen_width * 0.8)),
                'dialog_height': min(180, int(screen_height * 0.5)),
                'font_size': 9,
                'use_compact_mode': True
            }
        elif screen_width <= 1366 or screen_height <= 1024:
            # شاشة متوسطة
            return {
                'type': 'medium',
                'max_message_length': 120,
                'dialog_width': min(400, int(screen_width * 0.6)),
                'dialog_height': min(250, int(screen_height * 0.6)),
                'font_size': 10,
                'use_compact_mode': False
            }
        else:
            # شاشة كبيرة
            return {
                'type': 'large',
                'max_message_length': 200,
                'dialog_width': min(500, int(screen_width * 0.4)),
                'dialog_height': min(350, int(screen_height * 0.5)),
                'font_size': 11,
                'use_compact_mode': False
            }
    except:
        # إعدادات افتراضية آمنة في حالة الخطأ
        return {
            'type': 'safe',
            'max_message_length': 100,
            'dialog_width': 350,
            'dialog_height': 200,
            'font_size': 9,
            'use_compact_mode': True
        }

# دالة لتقصير النص بناءً على حجم الشاشة
def adapt_message_to_screen(message):
    """تكييف النص مع حجم الشاشة"""
    try:
        settings = get_screen_adaptive_settings()
        max_length = settings['max_message_length']

        if len(message) > max_length:
            return message[:max_length-3] + "..."
        return message
    except:
        return message

# وظيفة للتحقق من صحة البريد الإلكتروني
def is_valid_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

# وظيفة للتحقق من صحة رقم الهاتف
def is_valid_phone(phone):
    pattern = r'^\+?[0-9]{10,15}$'
    return re.match(pattern, phone) is not None

# وظيفة لعرض رسالة خطأ مع التكيف مع الشاشة
def show_error_message(title, message):
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)

    # تطبيق التكيف المتطور مع الشاشة
    try:
        # الحصول على إعدادات التكيف
        settings = get_screen_adaptive_settings()

        # تكييف النص مع حجم الشاشة
        message = adapt_message_to_screen(message)
        msg_box.setText(message)

        # تطبيق حجم الخط المناسب
        from PyQt5.QtGui import QFont
        font = QFont()
        font.setPointSize(settings['font_size'])
        msg_box.setFont(font)

        # تحديد حجم مربع الحوار بدقة
        msg_box.resize(settings['dialog_width'], settings['dialog_height'])

        # التأكد من أن مربع الحوار لا يتجاوز حدود الشاشة
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # حساب الموقع الآمن
        safe_x = max(10, (screen_geometry.width() - msg_box.width()) // 2)
        safe_y = max(10, (screen_geometry.height() - msg_box.height()) // 2)

        # التأكد من عدم تجاوز الحدود
        if safe_x + msg_box.width() > screen_geometry.width() - 10:
            safe_x = screen_geometry.width() - msg_box.width() - 10
        if safe_y + msg_box.height() > screen_geometry.height() - 10:
            safe_y = screen_geometry.height() - msg_box.height() - 10

        msg_box.move(safe_x, safe_y)

        # تطبيق الوضع المضغوط إذا لزم الأمر
        if settings['use_compact_mode']:
            msg_box.setWindowFlags(msg_box.windowFlags() | 0x00000001)  # Qt.WindowStaysOnTopHint

    except Exception as e:
        print(f"خطأ في التكيف مع الشاشة: {str(e)}")

    # تطبيق السمة على مربع الحوار
    try:
        from ui.theme_manager import ThemeManager
        from database import get_session, get_setting
        session = get_session()
        theme = get_setting(session, 'theme', 'light')
        ThemeManager.apply_theme_to_dialog(msg_box, theme)
    except:
        pass

    msg_box.exec_()

# وظيفة لعرض رسالة معلومات مع التكيف مع الشاشة
def show_info_message(title, message):
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Information)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)

    # تطبيق التكيف المتطور مع الشاشة
    try:
        # الحصول على إعدادات التكيف
        settings = get_screen_adaptive_settings()

        # تكييف النص مع حجم الشاشة
        message = adapt_message_to_screen(message)
        msg_box.setText(message)

        # تطبيق حجم الخط المناسب
        from PyQt5.QtGui import QFont
        font = QFont()
        font.setPointSize(settings['font_size'])
        msg_box.setFont(font)

        # تحديد حجم مربع الحوار بدقة
        msg_box.resize(settings['dialog_width'], settings['dialog_height'])

        # التأكد من أن مربع الحوار لا يتجاوز حدود الشاشة
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # حساب الموقع الآمن
        safe_x = max(10, (screen_geometry.width() - msg_box.width()) // 2)
        safe_y = max(10, (screen_geometry.height() - msg_box.height()) // 2)

        # التأكد من عدم تجاوز الحدود
        if safe_x + msg_box.width() > screen_geometry.width() - 10:
            safe_x = screen_geometry.width() - msg_box.width() - 10
        if safe_y + msg_box.height() > screen_geometry.height() - 10:
            safe_y = screen_geometry.height() - msg_box.height() - 10

        msg_box.move(safe_x, safe_y)

        # تطبيق الوضع المضغوط إذا لزم الأمر
        if settings['use_compact_mode']:
            msg_box.setWindowFlags(msg_box.windowFlags() | 0x00000001)

    except Exception as e:
        print(f"خطأ في التكيف مع الشاشة: {str(e)}")

    # تطبيق السمة على مربع الحوار
    try:
        from ui.theme_manager import ThemeManager
        from database import get_session, get_setting
        session = get_session()
        theme = get_setting(session, 'theme', 'light')
        ThemeManager.apply_theme_to_dialog(msg_box, theme)
    except:
        pass

    msg_box.exec_()

# وظيفة لعرض رسالة تأكيد مع التكيف مع الشاشة
def show_confirmation_message(title, message):
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)

    # تطبيق التكيف المتطور مع الشاشة
    try:
        # الحصول على إعدادات التكيف
        settings = get_screen_adaptive_settings()

        # تكييف النص مع حجم الشاشة
        message = adapt_message_to_screen(message)
        msg_box.setText(message)

        # تطبيق حجم الخط المناسب
        from PyQt5.QtGui import QFont
        font = QFont()
        font.setPointSize(settings['font_size'])
        msg_box.setFont(font)

        # تحديد حجم مربع الحوار بدقة
        msg_box.resize(settings['dialog_width'], settings['dialog_height'])

        # التأكد من أن مربع الحوار لا يتجاوز حدود الشاشة
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # حساب الموقع الآمن
        safe_x = max(10, (screen_geometry.width() - msg_box.width()) // 2)
        safe_y = max(10, (screen_geometry.height() - msg_box.height()) // 2)

        # التأكد من عدم تجاوز الحدود
        if safe_x + msg_box.width() > screen_geometry.width() - 10:
            safe_x = screen_geometry.width() - msg_box.width() - 10
        if safe_y + msg_box.height() > screen_geometry.height() - 10:
            safe_y = screen_geometry.height() - msg_box.height() - 10

        msg_box.move(safe_x, safe_y)

        # تطبيق الوضع المضغوط إذا لزم الأمر
        if settings['use_compact_mode']:
            msg_box.setWindowFlags(msg_box.windowFlags() | 0x00000001)

    except Exception as e:
        print(f"خطأ في التكيف مع الشاشة: {str(e)}")

    # تطبيق السمة على مربع الحوار
    try:
        from ui.theme_manager import ThemeManager
        from database import get_session, get_setting
        session = get_session()
        theme = get_setting(session, 'theme', 'light')
        ThemeManager.apply_theme_to_dialog(msg_box, theme)
    except:
        pass

    return msg_box.exec_() == QMessageBox.Yes

# وظيفة لتحويل التاريخ من QDate إلى datetime
def qdate_to_datetime(qdate):
    return datetime.datetime(qdate.year(), qdate.month(), qdate.day())

# وظيفة لتحويل التاريخ من datetime إلى QDate
def datetime_to_qdate(dt):
    return QDate(dt.year, dt.month, dt.day)

# وظيفة لحساب الرصيد المتبقي للفاتورة
def calculate_invoice_balance(invoice):
    return invoice.total_amount - invoice.paid_amount

# وظيفة لتنسيق المبلغ المالي بدون كسور عشرية مع وحدة الجنيه
def format_currency(amount):
    # تقريب المبلغ إلى أقرب عدد صحيح
    amount_int = int(round(amount))
    # تنسيق المبلغ بدون كسور عشرية مع وحدة الجنيه
    return f"{amount_int:,} جنيه"

# وظيفة لتنسيق التاريخ بشكل مناسب للغة العربية
def format_date(date):
    if not date:
        return "غير متوفر"

    # تحويل التاريخ إلى نص بتنسيق مناسب للغة العربية
    if isinstance(date, datetime.date) or isinstance(date, datetime.datetime):
        # تنسيق التاريخ: اليوم/الشهر/السنة
        return date.strftime("%d/%m/%Y")
    elif isinstance(date, str):
        # محاولة تحويل النص إلى تاريخ
        try:
            date_obj = datetime.datetime.strptime(date, "%Y-%m-%d")
            return date_obj.strftime("%d/%m/%Y")
        except ValueError:
            return date
    else:
        return str(date)

# وظيفة لتنسيق الكميات والأعداد بدون كسور عشرية
def format_quantity(quantity):
    # تقريب الكمية إلى أقرب عدد صحيح
    quantity_int = int(round(quantity))
    # تنسيق الكمية بدون كسور عشرية
    return f"{quantity_int}"

# وظيفة لحساب إجمالي الإيرادات في فترة زمنية معينة
def calculate_total_revenue(session, start_date, end_date):
    from database import Revenue
    revenues = session.query(Revenue).filter(
        Revenue.date >= start_date,
        Revenue.date <= end_date
    ).all()
    return sum(revenue.amount for revenue in revenues)

# وظيفة لحساب إجمالي المصروفات في فترة زمنية معينة
def calculate_total_expenses(session, start_date, end_date):
    from database import Expense, Salary
    expenses = session.query(Expense).filter(
        Expense.date >= start_date,
        Expense.date <= end_date
    ).all()

    salaries = session.query(Salary).filter(
        Salary.payment_date >= start_date,
        Salary.payment_date <= end_date
    ).all()

    total_expenses = sum(expense.amount for expense in expenses)
    total_salaries = sum(salary.amount for salary in salaries)

    return total_expenses + total_salaries

# وظيفة لحساب صافي الربح في فترة زمنية معينة
def calculate_net_profit(session, start_date, end_date):
    total_revenue = calculate_total_revenue(session, start_date, end_date)
    total_expenses = calculate_total_expenses(session, start_date, end_date)
    return total_revenue - total_expenses

# وظيفة لإنشاء رقم فاتورة جديد
def generate_invoice_number(session):
    from database import Invoice
    current_year = datetime.datetime.now().year
    last_invoice = session.query(Invoice).order_by(Invoice.id.desc()).first()

    if last_invoice:
        last_number = int(last_invoice.invoice_number.split('-')[1])
        new_number = last_number + 1
    else:
        new_number = 1

    return f"INV-{new_number:04d}-{current_year}"



